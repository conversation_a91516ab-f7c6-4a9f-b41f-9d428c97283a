/**
 * Audio utilities for the app
 * Uses expo-av for recording since expo-audio requires hooks
 */

import { Platform, PermissionsAndroid } from "react-native";

// Import expo-av for recording functionality
let ExpoAVAudio = null;
try {
  const ExpoAV = require("expo-av");
  ExpoAVAudio = ExpoAV.Audio;
  console.log("✅ expo-av Audio imported successfully");
} catch (error) {
  console.error("❌ Failed to import expo-av:", error);
}

/**
 * Get platform-specific recording options
 * @returns {Object} - Platform-specific recording options
 */
export const getPlatformRecordingOptions = () => {
  // If expo-av preset is available, use it directly
  if (ExpoAVAudio && ExpoAVAudio.RECORDING_OPTIONS_PRESET_HIGH_QUALITY) {
    console.log("🎤 Using expo-av built-in recording preset");
    return ExpoAVAudio.RECORDING_OPTIONS_PRESET_HIGH_QUALITY;
  }

  // Otherwise, use our custom platform-specific options
  const options = RECORDING_OPTIONS_PRESETS.HIGH_QUALITY;

  // Check if options has platform-specific structure
  if (options && typeof options === 'object' && (options.android || options.ios || options.web)) {
    if (Platform.OS === "android" && options.android) {
      return options.android;
    } else if (Platform.OS === "ios" && options.ios) {
      return options.ios;
    } else if (options.web) {
      return options.web;
    }
  }

  // Fallback to the options object itself if it's already platform-specific
  return options || {
    android: {
      extension: '.m4a',
      outputFormat: 'mpeg_4',
      audioEncoder: 'aac',
      sampleRate: 44100,
      numberOfChannels: 2,
      bitRate: 128000,
    },
    ios: {
      extension: '.m4a',
      outputFormat: 'mpeg4_aac',
      audioQuality: 'high',
      sampleRate: 44100,
      numberOfChannels: 2,
      bitRate: 128000,
      linearPCM: false,
    },
    web: {
      mimeType: 'audio/webm;codecs=opus',
      bitsPerSecond: 128000,
    },
  };
};

// Recording options presets for expo-av
export const RECORDING_OPTIONS_PRESETS = {
  HIGH_QUALITY: {
    android: {
      extension: '.m4a',
      outputFormat: 'mpeg_4',
      audioEncoder: 'aac',
      sampleRate: 44100,
      numberOfChannels: 2,
      bitRate: 128000,
    },
    ios: {
      extension: '.m4a',
      outputFormat: 'mpeg4_aac',
      audioQuality: 'high',
      sampleRate: 44100,
      numberOfChannels: 2,
      bitRate: 128000,
      linearPCM: false,
    },
    web: {
      mimeType: 'audio/webm;codecs=opus',
      bitsPerSecond: 128000,
    },
  },
};

/**
 * Request audio recording permissions using platform-specific APIs
 * @returns {Promise<Object>} - Promise resolving to { status, canAskAgain, granted }
 */
export const requestRecordingPermissions = async () => {
  try {
    console.log("🎤 Requesting audio recording permissions...");

    if (Platform.OS === "android") {
      // Use React Native's PermissionsAndroid for Android
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        {
          title: "Audio Recording Permission",
          message:
            "This app needs access to your microphone to record pronunciation.",
          buttonNeutral: "Ask Me Later",
          buttonNegative: "Cancel",
          buttonPositive: "OK",
        }
      );

      const isGranted = granted === PermissionsAndroid.RESULTS.GRANTED;
      console.log(`📱 Android permissions result: ${granted} (granted: ${isGranted})`);
      
      return {
        status: isGranted ? "granted" : "denied",
        granted: isGranted,
        canAskAgain: granted !== PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN,
      };
    } else if (Platform.OS === "ios") {
      // For iOS, use expo-av
      try {
        if (ExpoAVAudio && ExpoAVAudio.requestPermissionsAsync) {
          const requestResult = await ExpoAVAudio.requestPermissionsAsync();
          console.log("📱 iOS permission request result:", requestResult);
          return requestResult;
        }
        
        return permission;
      } catch (error) {
        console.error("❌ iOS permissions request failed:", error);
        // Return mock permissions for development
        return {
          status: "granted",
          granted: true,
          canAskAgain: true,
        };
      }
    } else {
      // Web platform
      try {
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
          await navigator.mediaDevices.getUserMedia({ audio: true });
          console.log("🌐 Web audio permissions granted");
          return {
            status: "granted",
            granted: true,
            canAskAgain: true,
          };
        }
      } catch (error) {
        console.error("❌ Web audio permissions denied:", error);
        return {
          status: "denied",
          granted: false,
          canAskAgain: true,
        };
      }

      return {
        status: "granted",
        granted: true,
        canAskAgain: true,
      };
    }
  } catch (error) {
    console.error("❌ Error requesting recording permissions:", error);

    // Return mock permissions for development
    return {
      status: "granted",
      granted: true,
      canAskAgain: true,
    };
  }
};

/**
 * Set audio mode for the app
 * @param {Object} options - Audio mode options
 * @returns {Promise<void>}
 */
export const setAudioMode = async (options = {}) => {
  try {
    console.log("🎵 Setting audio mode with options:", options);

    // expo-av audio mode configuration - only use supported properties
    const audioModeConfig = {
      allowsRecordingIOS: options.allowsRecordingIOS || false,
      playsInSilentModeIOS: options.playsInSilentModeIOS !== undefined ? options.playsInSilentModeIOS : true,
      staysActiveInBackground: options.staysActiveInBackground || false,
      shouldDuckAndroid: options.shouldDuckAndroid !== undefined ? options.shouldDuckAndroid : true,
    };

    console.log("🎵 Final audio mode config:", audioModeConfig);

    // Use expo-av's setAudioModeAsync
    if (ExpoAVAudio && ExpoAVAudio.setAudioModeAsync) {
      await ExpoAVAudio.setAudioModeAsync(audioModeConfig);
      console.log("✅ Audio mode set successfully");
    } else {
      console.warn("⚠️ setAudioModeAsync not available");
    }
  } catch (error) {
    console.error("❌ Error setting audio mode:", error);
    console.error("❌ Audio mode config that failed:", audioModeConfig);
    // Don't throw error as this is not critical for basic functionality
  }
};

/**
 * Recording class wrapper for expo-audio
 */
class RecordingWrapper {
  constructor() {
    try {
      console.log("🎤 Creating new recording wrapper...");

      // Use expo-av for recording
      if (ExpoAVAudio && ExpoAVAudio.Recording) {
        console.log("🎤 Using expo-av Recording");
        this._recording = new ExpoAVAudio.Recording();
      } else {
        throw new Error("expo-av Recording is not available");
      }

      console.log("✅ Recording wrapper created successfully");
    } catch (error) {
      console.error("❌ Error creating recording in wrapper:", error);
      console.error("❌ ExpoAVAudio.Recording available:", !!(ExpoAVAudio && ExpoAVAudio.Recording));
      throw new Error("Failed to create recording object. Please check expo-av installation.");
    }
  }

  async prepareToRecordAsync(options) {
    try {
      // Use platform-specific options if none provided
      const recordingOptions = options || getPlatformRecordingOptions();

      console.log("🎤 Platform:", Platform.OS);
      console.log("🎤 Raw options provided:", JSON.stringify(options, null, 2));
      console.log("🎤 Final recording options:", JSON.stringify(recordingOptions, null, 2));
      console.log("🎤 Options type:", typeof recordingOptions);
      console.log("🎤 Options keys:", recordingOptions ? Object.keys(recordingOptions) : 'null');

      // Validate options before passing to expo-av
      if (!recordingOptions) {
        throw new Error("Recording options are required");
      }

      if (typeof recordingOptions !== 'object') {
        throw new Error("Recording options must be an object");
      }

      const result = await this._recording.prepareToRecordAsync(recordingOptions);
      console.log("✅ Recording prepared successfully");
      return result;
    } catch (error) {
      console.error("❌ Error preparing recording:", error);
      console.error("❌ Original options:", JSON.stringify(options, null, 2));
      console.error("❌ Platform:", Platform.OS);
      console.error("❌ ExpoAVAudio available:", !!ExpoAVAudio);
      console.error("❌ ExpoAVAudio.RECORDING_OPTIONS_PRESET_HIGH_QUALITY:", ExpoAVAudio ? !!ExpoAVAudio.RECORDING_OPTIONS_PRESET_HIGH_QUALITY : 'N/A');
      throw error;
    }
  }

  async startAsync() {
    try {
      console.log("🎤 Starting recording...");
      const result = await this._recording.startAsync();
      console.log("✅ Recording started successfully");
      return result;
    } catch (error) {
      console.error("❌ Error starting recording:", error);
      throw error;
    }
  }

  async stopAsync() {
    try {
      console.log("🛑 Stopping recording...");
      const result = await this._recording.stopAsync();
      console.log("✅ Recording stopped successfully");
      return result;
    } catch (error) {
      console.error("❌ Error stopping recording:", error);
      throw error;
    }
  }

  async unloadAsync() {
    try {
      console.log("🗑️ Unloading recording...");
      const result = await this._recording.unloadAsync();
      console.log("✅ Recording unloaded successfully");
      return result;
    } catch (error) {
      console.error("❌ Error unloading recording:", error);
      throw error;
    }
  }

  getURI() {
    try {
      const uri = this._recording.getURI();
      console.log("📁 Recording URI:", uri);
      return uri;
    } catch (error) {
      console.error("❌ Error getting recording URI:", error);
      throw error;
    }
  }

  getStatus() {
    try {
      return this._recording.getStatus();
    } catch (error) {
      console.error("❌ Error getting recording status:", error);
      throw error;
    }
  }

  // Additional methods for compatibility
  async getStatusAsync() {
    try {
      return await this._recording.getStatusAsync();
    } catch (error) {
      console.error("❌ Error getting recording status async:", error);
      throw error;
    }
  }

  setOnRecordingStatusUpdate(callback) {
    try {
      this._recording.setOnRecordingStatusUpdate(callback);
    } catch (error) {
      console.error("❌ Error setting recording status update callback:", error);
    }
  }
}

/**
 * Create a sound object from a source
 * @param {Object} source - The audio source (e.g., { uri: 'https://example.com/audio.mp3' })
 * @param {Object} initialStatus - Initial playback status
 * @param {Function} onPlaybackStatusUpdate - Callback for playback status updates
 * @returns {Promise<Object>} - Promise resolving to { sound } object
 */
export const createSound = async (
  source,
  initialStatus = {},
  onPlaybackStatusUpdate = null
) => {
  try {
    console.log("🔊 Creating sound from source:", source);
    
    if (!ExpoAVAudio || !ExpoAVAudio.Sound) {
      throw new Error("expo-av Sound is not available");
    }

    const sound = new ExpoAVAudio.Sound();
    await sound.loadAsync(source, initialStatus);

    if (onPlaybackStatusUpdate) {
      sound.setOnPlaybackStatusUpdate(onPlaybackStatusUpdate);
    }

    console.log("✅ Sound created successfully");
    return { sound };
  } catch (error) {
    console.error("❌ Error creating sound:", error);
    throw error;
  }
};

// Export a compatibility layer for expo-av migration
export const Audio = {
  Sound: {
    createAsync: createSound,
  },
  Recording: RecordingWrapper,
  setAudioModeAsync: setAudioMode,
  requestPermissionsAsync: requestRecordingPermissions,
  getPermissionsAsync: () => ExpoAVAudio ? ExpoAVAudio.getPermissionsAsync() : null,
  RecordingOptionsPresets: {
    HIGH_QUALITY: getPlatformRecordingOptions(),
    ...RECORDING_OPTIONS_PRESETS,
  },
};

// Export individual functions for direct use
export {
  setAudioMode as setAudioModeAsync,
  requestRecordingPermissions as requestPermissionsAsync,
  RecordingWrapper as Recording,
  RECORDING_OPTIONS_PRESETS as RecordingOptionsPresets,
};

// Default export
export default Audio;