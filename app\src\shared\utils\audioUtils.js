/**
 * Audio utilities for the app
 * Updated to use expo-audio instead of deprecated expo-av
 */

import * as ExpoAudio from "expo-audio";
import { Platform, PermissionsAndroid } from "react-native";

// Audio recording options presets - compatible with expo-audio
export const RECORDING_OPTIONS_PRESETS = {
  HIGH_QUALITY: {
    android: {
      extension: ".m4a",
      outputFormat: ExpoAudio.AndroidOutputFormat.MPEG_4,
      audioEncoder: ExpoAudio.AndroidAudioEncoder.AAC,
      sampleRate: 44100,
      numberOfChannels: 2,
      bitRate: 128000,
    },
    ios: {
      extension: ".m4a",
      outputFormat: ExpoAudio.IOSOutputFormat.MPEG4AAC,
      audioQuality: ExpoAudio.IOSAudioQuality.HIGH,
      sampleRate: 44100,
      numberOfChannels: 2,
      bitRate: 128000,
      linearPCMBitDepth: 16,
      linearPCMIsBigEndian: false,
      linearPCMIsFloat: false,
    },
    web: {
      mimeType: "audio/webm;codecs=opus",
      bitsPerSecond: 128000,
    },
  },
};

/**
 * Request audio recording permissions using platform-specific APIs
 * @returns {Promise<Object>} - Promise resolving to { status, canAskAgain, granted }
 */
export const requestRecordingPermissions = async () => {
  try {
    console.log("🎤 Requesting audio recording permissions...");

    if (Platform.OS === "android") {
      // Use React Native's PermissionsAndroid for Android
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        {
          title: "Audio Recording Permission",
          message:
            "This app needs access to your microphone to record pronunciation.",
          buttonNeutral: "Ask Me Later",
          buttonNegative: "Cancel",
          buttonPositive: "OK",
        }
      );

      const isGranted = granted === PermissionsAndroid.RESULTS.GRANTED;
      console.log(`📱 Android permissions result: ${granted} (granted: ${isGranted})`);
      
      return {
        status: isGranted ? "granted" : "denied",
        granted: isGranted,
        canAskAgain: granted !== PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN,
      };
    } else if (Platform.OS === "ios") {
      // For iOS, use expo-audio
      try {
        const permission = await ExpoAudio.getPermissionsAsync();
        console.log("📱 iOS current permissions:", permission);
        
        if (permission.status !== "granted") {
          const requestResult = await ExpoAudio.requestPermissionsAsync();
          console.log("📱 iOS permission request result:", requestResult);
          return requestResult;
        }
        
        return permission;
      } catch (error) {
        console.error("❌ iOS permissions request failed:", error);
        // Return mock permissions for development
        return {
          status: "granted",
          granted: true,
          canAskAgain: true,
        };
      }
    } else {
      // Web platform
      try {
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
          await navigator.mediaDevices.getUserMedia({ audio: true });
          console.log("🌐 Web audio permissions granted");
          return {
            status: "granted",
            granted: true,
            canAskAgain: true,
          };
        }
      } catch (error) {
        console.error("❌ Web audio permissions denied:", error);
        return {
          status: "denied",
          granted: false,
          canAskAgain: true,
        };
      }

      return {
        status: "granted",
        granted: true,
        canAskAgain: true,
      };
    }
  } catch (error) {
    console.error("❌ Error requesting recording permissions:", error);

    // Return mock permissions for development
    return {
      status: "granted",
      granted: true,
      canAskAgain: true,
    };
  }
};

/**
 * Set audio mode for the app
 * @param {Object} options - Audio mode options
 * @returns {Promise<void>}
 */
export const setAudioMode = async (options = {}) => {
  try {
    console.log("🎵 Setting audio mode with options:", options);

    // expo-audio uses a different approach for audio mode
    const audioModeConfig = {
      allowsRecordingIOS: options.allowsRecordingIOS || false,
      playsInSilentModeIOS: options.playsInSilentModeIOS !== undefined ? options.playsInSilentModeIOS : true,
      staysActiveInBackground: options.staysActiveInBackground || false,
      shouldDuckAndroid: options.shouldDuckAndroid !== undefined ? options.shouldDuckAndroid : true,
      playThroughEarpieceAndroid: options.playThroughEarpieceAndroid || false,
    };

    // Use expo-audio's setAudioModeAsync
    await ExpoAudio.setAudioModeAsync(audioModeConfig);
    console.log("✅ Audio mode set successfully");
  } catch (error) {
    console.error("❌ Error setting audio mode:", error);
    // Don't throw error as this is not critical for basic functionality
  }
};

/**
 * Recording class wrapper for expo-audio
 */
class RecordingWrapper {
  constructor() {
    try {
      console.log("🎤 Creating new recording wrapper...");
      this._recording = new ExpoAudio.Recording();
      console.log("✅ Recording wrapper created successfully");
    } catch (error) {
      console.error("❌ Error creating recording in wrapper:", error);
      throw new Error("Failed to create recording object. Please check expo-audio installation.");
    }
  }

  async prepareToRecordAsync(options = RECORDING_OPTIONS_PRESETS.HIGH_QUALITY) {
    try {
      console.log("🎤 Preparing recording with options:", options);
      const result = await this._recording.prepareToRecordAsync(options);
      console.log("✅ Recording prepared successfully");
      return result;
    } catch (error) {
      console.error("❌ Error preparing recording:", error);
      throw error;
    }
  }

  async startAsync() {
    try {
      console.log("🎤 Starting recording...");
      const result = await this._recording.startAsync();
      console.log("✅ Recording started successfully");
      return result;
    } catch (error) {
      console.error("❌ Error starting recording:", error);
      throw error;
    }
  }

  async stopAsync() {
    try {
      console.log("🛑 Stopping recording...");
      const result = await this._recording.stopAsync();
      console.log("✅ Recording stopped successfully");
      return result;
    } catch (error) {
      console.error("❌ Error stopping recording:", error);
      throw error;
    }
  }

  async unloadAsync() {
    try {
      console.log("🗑️ Unloading recording...");
      const result = await this._recording.unloadAsync();
      console.log("✅ Recording unloaded successfully");
      return result;
    } catch (error) {
      console.error("❌ Error unloading recording:", error);
      throw error;
    }
  }

  getURI() {
    try {
      const uri = this._recording.getURI();
      console.log("📁 Recording URI:", uri);
      return uri;
    } catch (error) {
      console.error("❌ Error getting recording URI:", error);
      throw error;
    }
  }

  getStatus() {
    try {
      return this._recording.getStatus();
    } catch (error) {
      console.error("❌ Error getting recording status:", error);
      throw error;
    }
  }

  // Additional methods for compatibility
  async getStatusAsync() {
    try {
      return await this._recording.getStatusAsync();
    } catch (error) {
      console.error("❌ Error getting recording status async:", error);
      throw error;
    }
  }

  setOnRecordingStatusUpdate(callback) {
    try {
      this._recording.setOnRecordingStatusUpdate(callback);
    } catch (error) {
      console.error("❌ Error setting recording status update callback:", error);
    }
  }
}

/**
 * Create a sound object from a source
 * @param {Object} source - The audio source (e.g., { uri: 'https://example.com/audio.mp3' })
 * @param {Object} initialStatus - Initial playback status
 * @param {Function} onPlaybackStatusUpdate - Callback for playback status updates
 * @returns {Promise<Object>} - Promise resolving to { sound } object
 */
export const createSound = async (
  source,
  initialStatus = {},
  onPlaybackStatusUpdate = null
) => {
  try {
    console.log("🔊 Creating sound from source:", source);
    
    const sound = new ExpoAudio.Sound();
    await sound.loadAsync(source, initialStatus);

    if (onPlaybackStatusUpdate) {
      sound.setOnPlaybackStatusUpdate(onPlaybackStatusUpdate);
    }

    console.log("✅ Sound created successfully");
    return { sound };
  } catch (error) {
    console.error("❌ Error creating sound:", error);
    throw error;
  }
};

// Export a compatibility layer for expo-av migration
export const Audio = {
  Sound: {
    createAsync: createSound,
  },
  Recording: RecordingWrapper,
  setAudioModeAsync: setAudioMode,
  requestPermissionsAsync: requestRecordingPermissions,
  getPermissionsAsync: () => ExpoAudio.getPermissionsAsync(),
  RecordingOptionsPresets: RECORDING_OPTIONS_PRESETS,
  
  // Additional exports from expo-audio
  ...ExpoAudio,
};

// Export individual functions for direct use
export {
  createSound,
  setAudioMode as setAudioModeAsync,
  requestRecordingPermissions as requestPermissionsAsync,
  RecordingWrapper as Recording,
  RECORDING_OPTIONS_PRESETS as RecordingOptionsPresets,
};

// Default export
export default Audio;